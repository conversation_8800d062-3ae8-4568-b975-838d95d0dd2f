"use client";

import { AffiliateLink, AffiliateSection } from "@/components/AffiliateSection";
import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { Success<PERSON>on<PERSON>tti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const pdfToWordFAQs = [
  {
    question: "What types of PDFs can be converted to Word?",
    answer:
      "Our converter works best with text-based PDFs created from digital sources. It can extract text, tables, and basic formatting. Scanned PDFs or image-based PDFs may require OCR processing first.",
  },
  {
    question: "Will the formatting be preserved when converting PDF to Word?",
    answer:
      "We preserve basic formatting including text styles, paragraphs, and simple tables. Complex layouts, advanced graphics, or unusual fonts may not convert perfectly, but the text content will be fully editable.",
  },
  {
    question: "Can I convert password-protected PDFs to Word?",
    answer:
      "Currently, password-protected PDFs need to be unlocked before conversion. Please remove the password protection and try again.",
  },
  {
    question: "What Word format do you convert to?",
    answer:
      "We convert PDFs to modern .docx format (Word 2007 and later), which is compatible with Microsoft Word, Google Docs, and other word processors.",
  },
  {
    question: "Why convert PDF to Word?",
    answer:
      "Converting PDF to Word allows you to edit content that was previously locked in PDF format. This is perfect for updating documents, extracting text for reuse, or making corrections to existing content.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "PDF to Word Converter",
  description:
    "Free online PDF to Word converter. Convert PDF files to editable DOCX format",
  url: "https://freefox.com/pdf-to-word",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Convert PDF to DOCX",
    "Extract text and tables",
    "Preserve basic formatting",
    "No file size limits",
  ],
};

export default function PDFToWordPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [showSuccessState, setShowSuccessState] = useState(false);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);

  // Set page metadata on client side
  useEffect(() => {
    document.title =
      "Free PDF to Word Converter Online - Convert PDF to DOCX | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Convert PDF to Word online for free. Extract text and tables from PDF to editable DOCX format. No registration required. Fast and secure conversion."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/pdf-to-word");

    const fetchAffiliateLinks = async () => {
      try {
        // Fetch the JSON file from the public directory
        const response = await fetch("/data/pdf-to-word-affiliate.json");
        const data = await response.json();
        setAffiliateLinks(data);
      } catch (error) {
        console.error("Failed to fetch affiliate links:", error);
      }
    };

    fetchAffiliateLinks();
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleStartOver = () => {
    setFiles([]);
    setShowSuccessState(false);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select a PDF document to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      const formData = new FormData();
      formData.append("file", files[0]);

      const file = files[0];
      const chunkSize = 1024 * 1024;

      if (file.size > chunkSize) {
        const chunks = Math.ceil(file.size / chunkSize);
        for (let j = 0; j < chunks; j++) {
          // Simulate chunk upload progress
          await new Promise((resolve) => setTimeout(resolve, 50));
          const fileProgress = (j + 1) / chunks;
          const overallProgress = 10 + 70 * fileProgress;
          setProgress(Math.min(80, Math.round(overallProgress)));
        }
      }

      setProgress(80);

      const response = await apiPost("pdf-to-word", formData);

      setProgress(90);

      const fileName = files[0].name.replace(/\.pdf$/i, ".docx");

      await downloadFromResponse(response, fileName);

      setProgress(100);
      setShowConfetti(true);
      setShowSuccessState(true);
    } catch (error) {
      console.error("Error converting PDF document to Word:", error);
      toast.error("Failed to convert PDF document to Word. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 500, damping: 25 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div className="mb-8 space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free PDF to Word Converter Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Convert PDF to editable DOCX format. Extract text and tables for
                easy editing. No registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Extract Text & Tables
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Editable DOCX Output
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Preserve Formatting
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{
                "application/pdf": [".pdf"],
              }}
              maxFiles={1}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              showSuccessState={showSuccessState}
              successMessage="PDF converted successfully and downloaded!"
              successButtonText="Convert Another PDF"
              onStartOver={handleStartOver}
            />

            {files.length > 0 && !showSuccessState && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleConvert}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Converting..." : "Convert to Word"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          {/* Affiliate Section */}
          <motion.div className="mt-6" variants={itemVariants}>
            <AffiliateSection
              title="⚙️ Edit-Ready Word Files from Any PDF"
              subtitle="Unlock and edit your PDFs using the best PDF to Word converters"
              links={affiliateLinks}
            />
          </motion.div>

          <motion.div
            className="mt-6 bg-muted/50 p-6 rounded-lg"
            variants={itemVariants}
          >
            <h2 className="text-xl font-semibold mb-4">
              About PDF to Word Conversion
            </h2>
            <div className="space-y-4 text-sm text-muted-foreground">
              <p>
                Our PDF to Word converter transforms PDF documents into editable
                Microsoft Word (.docx) files. This allows you to edit content
                that was previously locked in a PDF format.
              </p>
              <p>
                The conversion process extracts text, tables, and basic
                formatting from your PDF. While the converter works well with
                most PDFs, the quality of conversion depends on the structure
                and complexity of the original document.
              </p>
              <p>
                <strong>Best results with:</strong> Text-based PDFs created from
                digital sources (not scanned documents). For scanned PDFs, you
                may need to use OCR software first.
              </p>
              <p>
                <strong>Note:</strong> Your files are processed securely on our
                servers and are not stored permanently. They are automatically
                deleted after processing.
              </p>
            </div>
          </motion.div>

          {/* FAQ Section */}
          <motion.div variants={itemVariants}>
            <SEOFaq
              title="PDF to Word Conversion FAQ"
              faqs={pdfToWordFAQs}
              toolName="PDF to Word Converter"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
