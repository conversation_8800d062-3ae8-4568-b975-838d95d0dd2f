"use client";

import { AffiliateLink, AffiliateSection } from "@/components/AffiliateSection";
import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq, imageCompressionFAQs } from "@/components/SEOFaq";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Compress Image",
  description:
    "Free online image compressor to reduce file size while maintaining quality",
  url: "https://freefox.com/compress-image",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Compress JPEG images",
    "Compress PNG images",
    "Adjustable quality settings",
    "Batch processing",
  ],
};

// Helper function to format bytes
function formatBytes(bytes: number, decimals = 2) {
  if (!bytes || bytes === 0) return "0 Bytes";
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + " " + sizes[i];
}

interface CompressionStats {
  originalSize: number;
  compressedSize: number;
  ratio: string;
  originalFilename: string;
  quality: number;
}

export default function CompressImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [quality, setQuality] = useState(85); // Default quality is 85%
  const [compressionStats, setCompressionStats] =
    useState<CompressionStats | null>(null);
  const [uploaderKey, setUploaderKey] = useState(0);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);

  // Set page metadata on client side
  useEffect(() => {
    document.title =
      "Free Image Compressor Online - Reduce Image File Size | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Compress images online for free. Reduce JPEG, PNG, WebP file sizes while maintaining quality. Adjustable compression settings. No registration required."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/compress-image");

    const fetchAffiliateLinks = async () => {
      try {
        // Fetch the JSON file from the public directory
        const response = await fetch("/data/image-compression-affiliate.json");
        const data = await response.json();
        // Set the appropriate data into state
        setAffiliateLinks(data);
      } catch (error) {
        console.error("Failed to fetch affiliate links:", error);
      }
    };

    fetchAffiliateLinks();
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);
    setCompressionStats(null); // Clear previous stats if a new file is selected
  };

  const handleCompress = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to compress");
      return;
    }

    setIsProcessing(true);
    setProgress(0);
    setCompressionStats(null);

    const currentFile = files[0];

    try {
      setProgress(5);
      const formData = new FormData();
      formData.append("file", currentFile);
      formData.append("quality", quality.toString());

      // Start incremental progress updates
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return Math.round(prev + Math.random() * 3 + 1); // Increment by 1-4% randomly
        });
      }, 250);

      const response = await apiPost("compress-image", formData);

      clearInterval(progressInterval);

      setProgress(100);
      await downloadFromResponse(response, `compressed_${currentFile.name}`);

      // Get compression stats from response headers
      const originalSizeHeader = response.headers.get("X-Original-Size");
      const compressedSizeHeader = response.headers.get("X-Compressed-Size");
      const ratioHeader = response.headers.get("X-Compression-Ratio");

      if (originalSizeHeader && compressedSizeHeader && ratioHeader) {
        setCompressionStats({
          originalSize: parseInt(originalSizeHeader, 10),
          compressedSize: parseInt(compressedSizeHeader, 10),
          ratio: ratioHeader,
          originalFilename: currentFile.name,
          quality: quality,
        });
        toast.success(
          `"${currentFile.name}" compressed! Reduction: ${ratioHeader}`
        );
      } else {
        console.warn(
          "One or more compression headers were missing or null. Stats will not be displayed."
        );
        toast.success(
          `"${currentFile.name}" compressed and downloaded! (Stats unavailable)`
        );
      }

      setShowConfetti(true);
      setFiles([]); // Clear the file from parent state. This hides the "Compress Image" button.
      // EnhancedFileUpload will be replaced by stats view if compressionStats is set.
    } catch (error) {
      console.error("Error compressing image:", error);
      toast.error(
        `Failed to compress "${currentFile.name}". Please try again.`
      );
      setProgress(0);
    } finally {
      if (progress === 100) {
        setTimeout(() => {
          setIsProcessing(false);
          setProgress(0);
        }, 300);
      } else {
        setIsProcessing(false);
        setProgress(0);
      }
    }
  };

  const handleCompressAnother = () => {
    setCompressionStats(null);
    setFiles([]);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
    setQuality(85);
    setUploaderKey((prevKey) => prevKey + 1);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.div className="mb-8 space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free Image Compressor Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Reduce image file sizes while maintaining quality. Support for
                JPEG, PNG, WebP and more. Adjustable compression settings.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Multiple Formats
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Quality Control
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Lossless Options
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          {compressionStats ? (
            <motion.div
              className="mt-8 p-6 bg-muted/20 rounded-lg text-center"
              variants={itemVariants}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
            >
              <h2 className="text-2xl font-semibold mb-2">
                Compression Complete!
              </h2>
              <p className="text-lg mb-4 text-muted-foreground">
                <span className="font-medium">
                  {compressionStats.originalFilename}
                </span>{" "}
                has been compressed and downloaded.
              </p>
              <div className="space-y-2 text-left inline-block mb-6">
                <p>
                  <strong>Original Size:</strong>{" "}
                  {formatBytes(compressionStats.originalSize)}
                </p>
                <p>
                  <strong>Compressed Size:</strong>{" "}
                  {formatBytes(compressionStats.compressedSize)}
                </p>
                <p>
                  <strong>Reduction:</strong> {compressionStats.ratio}
                </p>
                <p>
                  <strong>Quality Used:</strong> {compressionStats.quality}%
                </p>
              </div>
              <div>
                <Button onClick={handleCompressAnother} className="px-8">
                  Compress Another Image
                </Button>
              </div>
            </motion.div>
          ) : (
            <motion.div className="space-y-6" variants={itemVariants}>
              <EnhancedFileUpload
                key={uploaderKey}
                acceptedFileTypes={{
                  "image/jpeg": [".jpg", ".jpeg"],
                  "image/png": [".png"],
                  "image/webp": [".webp"],
                  "image/gif": [".gif"],
                  "image/bmp": [".bmp"],
                  "image/tiff": [".tiff", ".tif"],
                  "image/heic": [".heic"],
                  "image/heif": [".heif"],
                }}
                maxFiles={1}
                onFilesSelected={handleFilesSelected}
                isProcessing={isProcessing}
                processingProgress={progress}
                replaceExisting={true}
              />

              {files.length > 0 && (
                <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                  <h3 className="font-medium font-display">
                    Compression Options
                  </h3>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Quality: {quality}%</span>
                      <span className="text-xs text-muted-foreground">
                        {quality < 50
                          ? "Low quality, smaller file size"
                          : quality < 80
                          ? "Balanced quality and file size"
                          : "High quality, larger file size"}
                      </span>
                    </div>
                    <Slider
                      value={[quality]}
                      min={1}
                      max={100}
                      step={1}
                      onValueChange={(value) => setQuality(value[0])}
                      disabled={isProcessing}
                    />
                  </div>
                </div>
              )}

              {files.length > 0 && (
                <div className="flex justify-end">
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button
                      onClick={handleCompress}
                      disabled={isProcessing}
                      className="px-8"
                    >
                      {isProcessing
                        ? progress < 20
                          ? "Preparing..."
                          : progress < 90
                          ? "Compressing..."
                          : progress < 100
                          ? "Downloading..."
                          : "Complete!"
                        : "Compress Image"}
                    </Button>
                  </motion.div>
                </div>
              )}
            </motion.div>
          )}

          {/* Affiliate Section */}
          <motion.div className="mt-6" variants={itemVariants}>
            <AffiliateSection
              title="🔥 Need Professional Image Compression?"
              subtitle="Explore professional Image tools for better compression service"
              links={affiliateLinks} // Pass the fetched links
            />
          </motion.div>

          <motion.div
            className="mt-6 bg-muted/50 p-6 rounded-lg"
            variants={itemVariants}
          >
            <h2 className="text-xl font-semibold mb-4">
              About Image Compression
            </h2>
            <div className="space-y-4 text-sm text-muted-foreground">
              <p>
                Our image compressor uses advanced algorithms to reduce the file
                size of your images while maintaining visual quality. This is
                useful for reducing website loading times, saving storage space,
                sharing images via email, and uploading to social media
                platforms.
              </p>
              <p>
                The quality slider lets you control the balance between file
                size and image quality. Higher values preserve more details but
                result in larger files, while lower values create smaller files
                with some quality loss.
              </p>
              <p>
                <strong>Note:</strong> Your files are processed securely on our
                servers and are not stored permanently. They are automatically
                deleted after processing.
              </p>
            </div>
          </motion.div>
          <motion.div variants={itemVariants}>
            <SEOFaq
              title="Image Compression FAQ"
              faqs={imageCompressionFAQs}
              toolName="Image Compressor"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
