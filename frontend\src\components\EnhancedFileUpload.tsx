import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { AnimatePresence, motion } from "framer-motion";
import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { toast } from "sonner";

interface FileUploadProps {
  acceptedFileTypes: Record<string, string[]>;
  maxFiles?: number;
  onFilesSelected: (files: File[]) => void;
  isProcessing?: boolean;
  processingProgress?: number;
  replaceExisting?: boolean;
  showSuccessState?: boolean;
  successMessage?: string;
  successButtonText?: string;
  onStartOver?: () => void;
}

export function EnhancedFileUpload({
  acceptedFileTypes,
  maxFiles = 10,
  onFilesSelected,
  isProcessing = false,
  processingProgress = 0,
  replaceExisting = false,
  showSuccessState = false,
  successMessage = "Operation completed successfully!",
  successButtonText = "Start Over",
  onStartOver,
}: FileUploadProps) {
  const [files, setFiles] = useState<File[]>([]);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      let newFiles: File[];

      // If replaceExisting is true, replace all existing files with new ones
      // Otherwise, append new files up to maxFiles
      if (replaceExisting) {
        newFiles = acceptedFiles.slice(0, maxFiles);

        // Show warning toast if replacing existing files
        if (files.length > 0 && acceptedFiles.length > 0) {
          toast.info("Previous file has been replaced");
        }
      } else {
        newFiles = [...files, ...acceptedFiles].slice(0, maxFiles);
      }

      setFiles(newFiles);
      onFilesSelected(newFiles);

      // Show animation when files are dropped
      if (acceptedFiles.length > 0) {
        if (!replaceExisting || files.length === 0) {
          toast.success(
            `${acceptedFiles.length} file${
              acceptedFiles.length !== 1 ? "s" : ""
            } added`
          );
        }
      }
    },
    [files, maxFiles, onFilesSelected, replaceExisting]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxFiles,
  });

  const removeFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
    onFilesSelected(newFiles);
  };

  const clearFiles = () => {
    setFiles([]);
    onFilesSelected([]);
  };

  const handleStartOver = () => {
    setFiles([]);
    onFilesSelected([]);
    if (onStartOver) {
      onStartOver();
    }
  };

  // Animation variants
  const dropzoneVariants = {
    initial: { opacity: 0.6, scale: 0.98 },
    animate: { opacity: 1, scale: 1 },
    hover: { scale: 1.02, boxShadow: "0 0 0 3px rgba(66, 153, 225, 0.5)" },
    active: { scale: 0.98 },
  };

  const fileItemVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, x: -10 },
  };

  // If showing success state, render success UI instead of upload UI
  if (showSuccessState) {
    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 text-center dark:bg-green-950/20 dark:border-green-800/50">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-3"
          >
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center dark:bg-green-900/50">
              <svg
                className="w-8 h-8 text-green-600 dark:text-green-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </motion.div>

          <motion.h3
            className="text-lg font-semibold text-green-800 dark:text-green-200 mb-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Success!
          </motion.h3>

          <motion.p
            className="text-green-700 dark:text-green-300 mb-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            {successMessage}
          </motion.p>

          <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
            <Button
              onClick={handleStartOver}
              className="px-6"
              variant="default"
            >
              {successButtonText}
            </Button>
          </motion.div>
        </div>
      </motion.div>
    );
  }

  return (
    <div className="w-full space-y-4">
      <motion.div
        className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
          isDragActive
            ? "border-primary bg-primary/10"
            : "border-muted-foreground/25 hover:border-primary/50"
        }`}
        variants={dropzoneVariants}
        initial="initial"
        animate="animate"
        whileHover="hover"
        whileTap="active"
      >
        <div {...getRootProps()} className="w-full h-full">
          <input {...getInputProps()} />
          <div className="flex flex-col items-center justify-center gap-2">
            <AnimatePresence>
              {
                <motion.div
                  key="upload"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="h-10 w-10 text-muted-foreground"
                  >
                    <path d="M4 14.899A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242"></path>
                    <path d="M12 12v9"></path>
                    <path d="m16 16-4-4-4 4"></path>
                  </svg>
                </motion.div>
              }
            </AnimatePresence>
            <motion.p
              className="text-sm text-muted-foreground"
              animate={{ opacity: isDragActive ? 0.5 : 1 }}
            >
              <span className="font-semibold">Click to upload</span> or drag and
              drop
            </motion.p>
            <p className="text-xs text-muted-foreground">
              {Object.entries(acceptedFileTypes)
                .map(
                  ([type, extensions]) => `${type} (${extensions.join(", ")})`
                )
                .join(", ")}
            </p>
            <p className="text-xs text-muted-foreground">
              Max {maxFiles} file{maxFiles !== 1 ? "s" : ""}
            </p>
          </div>
        </div>
      </motion.div>

      <AnimatePresence>
        {files.length > 0 && (
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: "auto" }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">
                {files.length} file{files.length !== 1 ? "s" : ""} selected
              </h3>
              <Button variant="outline" size="sm" onClick={clearFiles}>
                Clear all
              </Button>
            </div>
            <motion.ul className="space-y-2">
              <AnimatePresence>
                {files.map((file, index) => (
                  <motion.li
                    key={`${file.name}-${index}`}
                    className="flex items-center justify-between bg-muted/50 p-2 rounded-md text-sm"
                    variants={fileItemVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    transition={{ delay: index * 0.05 }}
                  >
                    <span className="truncate max-w-[250px]">{file.name}</span>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6"
                      onClick={() => removeFile(index)}
                      disabled={isProcessing}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="h-4 w-4"
                      >
                        <path d="M18 6 6 18"></path>
                        <path d="m6 6 12 12"></path>
                      </svg>
                    </Button>
                  </motion.li>
                ))}
              </AnimatePresence>
            </motion.ul>
          </motion.div>
        )}
      </AnimatePresence>

      <AnimatePresence>
        {isProcessing && (
          <motion.div
            className="space-y-2"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium">Processing...</h3>
              <span className="text-xs text-muted-foreground">
                {processingProgress}%
              </span>
            </div>
            <motion.div
              initial={{ width: "0%" }}
              animate={{ width: `${processingProgress}%` }}
              transition={{ duration: 0.5 }}
            >
              <Progress value={processingProgress} className="h-2" />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
