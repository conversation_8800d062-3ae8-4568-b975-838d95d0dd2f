"use client";

import { motion } from "framer-motion";
import { memo, useMemo } from "react";

export interface AffiliateLink {
  name: string;
  description: string;
  catchyPhrase: string;
  url: string;
  rating?: number;
  badge?: string;
}

interface AffiliateSectionProps {
  title: string;
  subtitle: string;
  links: AffiliateLink[];
  className?: string;
}

const StarRating = memo(
  ({ rating, badge }: { rating: number; badge?: string }) => (
    <div className="flex items-center gap-3">
      <div className="flex items-center gap-0.5 text-sm">
        {[...Array(5)].map((_, i) => (
          <span key={i}>
            {i < rating ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="orange"
                className="h-4 w-4"
              >
                <path d="M12 2l2.9 6.7L22 9.2l-5 5 1.2 7.8L12 18l-6.2 4 1.2-7.8-5-5 7.1-0.5L12 2z" />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 24 24"
                fill="white"
                stroke="orange"
                strokeWidth="1.5"
                className="h-4 w-4"
              >
                <path
                  d="M12 2l2.9 6.7L22 9.2l-5 5 1.2 7.8L12 18l-6.2 4 1.2-7.8-5-5 7.1-0.5L12 2z"
                  strokeLinejoin="round"
                />
              </svg>
            )}
          </span>
        ))}
        <span className="ml-1 text-sm font-medium text-gray-600 dark:text-gray-400">
          {rating}.0
        </span>
      </div>
      {badge && (
        <span className="rounded-full bg-gradient-to-r from-amber-500 to-orange-500 px-2.5 py-1 text-xs font-medium text-white shadow-sm">
          {badge}
        </span>
      )}
    </div>
  )
);

StarRating.displayName = "StarRating";

// Modern horizontal affiliate card
const AffiliateCard = memo(
  ({ link, index }: { link: AffiliateLink; index: number }) => {
    const itemVariants = useMemo(
      () => ({
        hidden: { opacity: 0, x: -20 },
        visible: {
          opacity: 1,
          x: 0,
          transition: { duration: 0.3, delay: index * 0.05 },
        },
      }),
      [index]
    );

    return (
      <motion.div
        className="group relative overflow-hidden rounded-3xl bg-gradient-to-r from-orange-100 to-amber-200 p-[2px]"
        variants={itemVariants}
        whileHover={{ scale: 1.01, transition: { duration: 0.15 } }}
        whileTap={{ scale: 0.98, transition: { duration: 0.1 } }}
      >
        <div className="flex items-center justify-between rounded-3xl bg-white/70 p-6 shadow-sm backdrop-blur-sm transition-all duration-200 hover:shadow-md hover:shadow-orange-100/30 dark:bg-gray-900/70 dark:hover:shadow-orange-900/15">
          {/* Left content section */}
          <div className="flex-1 pr-6">
            <div className="mb-2 flex items-center gap-4">
              <h3 className="text-xl font-bold text-gray-900 dark:text-gray-100">
                {link.name}
              </h3>
              {link.rating && (
                <StarRating rating={link.rating} badge={link.badge} />
              )}
              {!link.rating && link.badge && (
                <span className="rounded-full bg-gradient-to-r from-amber-500 to-orange-500 px-2.5 py-1 text-xs font-medium text-white shadow-sm">
                  {link.badge}
                </span>
              )}
            </div>

            <p className="mb-2 text-sm leading-relaxed text-gray-600 dark:text-gray-400">
              {link.description}
            </p>

            <p className="text-base font-semibold text-orange-600 dark:text-orange-400">
              {link.catchyPhrase}
            </p>
          </div>

          {/* Right action section */}
          <div className="flex-shrink-0">
            <a
              href={link.url}
              target="_blank"
              rel="noopener noreferrer"
              className="group/btn inline-flex items-center gap-2 rounded-md bg-gradient-to-r from-orange-500 to-orange-600 px-5 py-2.5 text-sm font-medium text-white shadow-lg transition-all duration-200 hover:scale-105 hover:from-orange-600 hover:to-orange-700 hover:shadow-lg hover:shadow-orange-200/30 dark:shadow-orange-900/30"
            >
              Try Now
              <span className="transition-transform duration-200 group-hover/btn:translate-x-1">
                ➔
              </span>
            </a>
          </div>

          {/* Subtle hover effect */}
          <div className="pointer-events-none absolute inset-0 rounded-3xl bg-gradient-to-r from-orange-500/5 to-amber-500/5 opacity-0 transition-opacity duration-200 group-hover:opacity-100" />
        </div>
      </motion.div>
    );
  }
);

AffiliateCard.displayName = "AffiliateCard";

export const AffiliateSection = memo(function AffiliateSection({
  title,
  subtitle,
  links,
  className = "",
}: AffiliateSectionProps) {
  const containerVariants = useMemo(
    () => ({
      hidden: { opacity: 0, y: 30 },
      visible: {
        opacity: 1,
        y: 0,
        transition: {
          duration: 0.4,
          staggerChildren: 0.05,
        },
      },
    }),
    []
  );

  const headerVariants = useMemo(
    () => ({
      hidden: { opacity: 0, y: 20 },
      visible: {
        opacity: 1,
        y: 0,
        transition: { duration: 0.3 },
      },
    }),
    []
  );

  return (
    <section className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-orange-100 to-amber-200 p-[2px]">
      <motion.section
        className={`rounded-[calc(1.5rem-2px)] bg-gradient-to-br from-gray-50/80 to-orange-50/40 p-6 backdrop-blur-sm ${className}`}
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Subtle background elements */}
        <div className="absolute -right-12 -top-12 h-48 w-48 rounded-full bg-gradient-to-br from-orange-200/20 to-amber-200/10 blur-3xl dark:from-orange-800/10 dark:to-amber-800/5" />
        <div className="absolute -bottom-6 -left-6 h-32 w-32 rounded-full bg-gradient-to-tr from-amber-200/15 to-orange-200/10 blur-2xl dark:from-amber-800/8 dark:to-orange-800/5" />

        <div className="relative z-10">
          {/* Header */}
          <motion.div className="mb-6 text-center" variants={headerVariants}>
            <h2 className="mb-2 bg-gradient-to-r from-gray-900 to-orange-800 bg-clip-text text-3xl font-bold text-transparent dark:from-gray-100 dark:to-orange-200">
              {title}
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-400">
              {subtitle}
            </p>
          </motion.div>

          {/* Cards stack */}
          <div className="space-y-4">
            {links.map((link, index) => (
              <AffiliateCard
                key={`${link.name}-${index}`}
                link={link}
                index={index}
              />
            ))}
          </div>

          <motion.div className="mt-6 text-center" variants={headerVariants}>
            <div className="inline-flex items-center gap-2 rounded-full px-4 py-2 text-sm font-medium text-orange-700 dark:from-gray-800 dark:to-gray-800 dark:text-orange-300">
              <span className="text-base">💡</span>
              <span>
                These tools complement our free service perfectly for
                professional workflows!
              </span>
            </div>
          </motion.div>
        </div>
      </motion.section>
    </section>
  );
});

// Demo usage
export default function Demo() {
  const sampleLinks: AffiliateLink[] = [
    {
      name: "Premium Design Tool",
      description:
        "Professional design suite with advanced features for creating stunning visuals and user interfaces.",
      catchyPhrase: "Design like a pro in minutes!",
      url: "#",
      rating: 5,
      badge: "Popular",
    },
    {
      name: "Analytics Platform",
      description:
        "Comprehensive analytics solution to track, measure, and optimize your business performance.",
      catchyPhrase: "Data-driven decisions made easy",
      url: "#",
      rating: 4,
      badge: "Recommended",
    },
    {
      name: "Productivity Suite",
      description:
        "All-in-one workspace for teams to collaborate, manage projects, and boost productivity.",
      catchyPhrase: "10x your team's efficiency",
      url: "#",
      badge: "New",
    },
  ];

  return (
    <div className="min-h-screen bg-gray-100 p-6 dark:bg-gray-950">
      <AffiliateSection
        title="Recommended Tools"
        subtitle="Carefully selected tools to supercharge your workflow"
        links={sampleLinks}
      />
    </div>
  );
}
