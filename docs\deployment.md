# FreeFox - Deployment Guide

This guide provides instructions for deploying the FreeFox application to a production environment.

## Prerequisites

- Node.js (v18 or later)
- Python (v3.8 or later)
- npm or yarn
- Git

## Deployment Options

There are several ways to deploy the FreeFox application:

1. **Traditional Server Deployment**: Deploy the frontend and backend on a traditional server
2. **Cloud Platform Deployment**: Deploy to cloud platforms like AWS, Azure, or Google Cloud
3. **Containerized Deployment**: Deploy using Docker containers

## Traditional Server Deployment

### Frontend Deployment

1. Build the Next.js application:

```bash
cd frontend
npm run build
```

2. Start the production server:

```bash
npm start
```

### Backend Deployment

1. Install production dependencies:

```bash
cd backend
pip install -r requirements.txt
```

2. Start the backend server:

```bash
python run.py
```

## Cloud Platform Deployment

### Deploying to Vercel (Frontend)

1. Install the Vercel CLI:

```bash
npm install -g vercel
```

2. Deploy the frontend:

```bash
cd frontend
vercel
```

### Deploying to Heroku (Backend)

1. Install the Heroku CLI and login:

```bash
npm install -g heroku
heroku login
```

2. Create a new Heroku app:

```bash
cd backend
heroku create freefox-backend
```

3. Create a Procfile in the backend directory:

```
web: uvicorn app.main:app --host=0.0.0.0 --port=$PORT
```

4. Deploy to Heroku:

```bash
git push heroku main
```

## Containerized Deployment (Docker)

### Creating Docker Images

1. Create a Dockerfile for the frontend in the frontend directory:

```dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .
RUN npm run build

EXPOSE 3001

CMD ["npm", "start"]
```

2. Create a Dockerfile for the backend in the backend directory:

```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

EXPOSE 8001

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8001"]
```

3. Build the Docker images:

```bash
cd frontend
docker build -t freefox-frontend .

cd ../backend
docker build -t freefox-backend .
```

4. Run the Docker containers:

```bash
docker run -d -p 3001:3001 freefox-frontend
docker run -d -p 8001:8001 freefox-backend
```

## Using Docker Compose

Create a docker-compose.yml file in the root directory:

```yaml
version: "3"

services:
  frontend:
    build: ./frontend
    ports:
      - "3001:3001"
    depends_on:
      - backend
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8001

  backend:
    build: ./backend
    ports:
      - "8001:8001"
```

Run with Docker Compose:

```bash
docker-compose up -d
```

## Production Considerations

### Environment Variables

Create appropriate environment files for different environments:

- `.env.development` for development
- `.env.production` for production

Include variables like:

- `NEXT_PUBLIC_API_URL`: URL of the backend API
- `PORT`: Port for the server to listen on

### CORS Configuration

Ensure the backend CORS settings are configured for your production domain:

```python
app.add_middleware(
    CORSMiddleware,
    allow_origins=["https://your-production-domain.com"],
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### Security

1. Use HTTPS for all production traffic
2. Set appropriate security headers
3. Implement rate limiting for API endpoints
4. Consider adding a CDN for static assets

### Monitoring and Logging

1. Implement application logging
2. Set up monitoring for server health
3. Configure alerts for critical errors

## Scaling for High Load

To handle 1000 simultaneous users:

1. **Frontend Scaling**:

   - Use a CDN for static assets
   - Implement caching strategies
   - Consider serverless deployment for automatic scaling

2. **Backend Scaling**:
   - Use multiple worker processes (Gunicorn with Uvicorn workers)
   - Implement load balancing
   - Consider containerization with Kubernetes for orchestration

## Troubleshooting

Common deployment issues:

1. **CORS errors**: Check CORS configuration in the backend
2. **API connection issues**: Verify API URL environment variables
3. **File size limitations**: Configure server to handle large file uploads
4. **Memory issues**: Adjust container memory limits for file processing
