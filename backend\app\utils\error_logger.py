"""
Enhanced Error Logging System for FreeFox Backend

Features:
- Daily log rotation with unique date-based filenames
- Automatic cleanup of logs older than 7 days
- Eye-soothing colored console output
- File-only error logging (no info/debug in files)
- Structured JSON format for easy parsing
- Request context tracking
"""

import json
import logging
import os
import sys
import traceback
from datetime import datetime, timed<PERSON><PERSON>
from enum import Enum
from logging.handlers import TimedRotatingFileHandler
from pathlib import Path
from typing import Any, Dict, Optional, Union

import colorama
from colorama import Fore, Style

# Initialize colorama for cross-platform colored output
colorama.init(autoreset=True)


class Operation(Enum):
    """Enumeration of logging operations with their corresponding icons"""

    # General operations
    STARTUP = ("startup", "🚀")
    SHUTDOWN = ("shutdown", "🛑")
    SUCCESS = ("success", "✅")
    FAILURE = ("failure", "💥")
    PROCESSING = ("processing", "⚙️")
    COMPLETED = ("completed", "🎉")

    # File operations
    FILE_UPLOAD = ("file_upload", "📤")
    FILE_DOWNLOAD = ("file_download", "📥")
    FILE_SAVE = ("file_save", "💾")
    FILE_DELETE = ("file_delete", "🗑️")
    FILE_NOT_FOUND = ("file_not_found", "📄❓")
    FILE_CORRUPTED = ("file_corrupted", "📄💔")

    # PDF operations
    PDF_MERGE = ("pdf_merge", "📄➕")
    PDF_SPLIT = ("pdf_split", "📄✂️")
    PDF_COMPRESS = ("pdf_compress", "📄🗜️")
    PDF_ROTATE = ("pdf_rotate", "📄🔄")
    PDF_CONVERT = ("pdf_convert", "📄🔄")

    # Image operations
    IMAGE_COMPRESS = ("image_compress", "🖼️🗜️")
    IMAGE_RESIZE = ("image_resize", "🖼️📐")
    IMAGE_CONVERT = ("image_convert", "🖼️🔄")
    IMAGE_CROP = ("image_crop", "🖼️✂️")
    IMAGE_UPSCALE = ("image_upscale", "🖼️⬆️")

    # Network operations
    REQUEST_RECEIVED = ("request_received", "🌐")
    RESPONSE_SENT = ("response_sent", "📡")
    TIMEOUT = ("timeout", "⏰")
    CONNECTION_ERROR = ("connection_error", "🔌❌")

    # System operations
    MEMORY_WARNING = ("memory_warning", "🧠⚠️")
    DISK_WARNING = ("disk_warning", "💽⚠️")
    CLEANUP = ("cleanup", "🧹")
    VALIDATION_ERROR = ("validation_error", "✅❌")
    PERMISSION_ERROR = ("permission_error", "🔒❌")

    def __init__(self, key: str, icon: str):
        self.key = key
        self.icon = icon

    @classmethod
    def get_icon(cls, operation: Union['Operation', str, None]) -> str:
        """Get icon for operation, supporting both enum and string values"""
        if operation is None:
            return ''

        if isinstance(operation, cls):
            return operation.icon

        if isinstance(operation, str):
            # Support legacy string operations
            for op in cls:
                if op.key == operation:
                    return op.icon
            return ''

        return ''

    def __str__(self) -> str:
        return self.key


class ColoredConsoleFormatter(logging.Formatter):
    """Custom formatter for eye-soothing colored console output"""

    COLORS = {
        'DEBUG': Fore.CYAN,
        'INFO': Fore.GREEN,
        'WARNING': Fore.YELLOW,
        'ERROR': Fore.RED,
        'CRITICAL': Fore.MAGENTA + Style.BRIGHT,
    }

    ICONS = {
        'DEBUG': '🔍',
        'INFO': '📝',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨',
    }

    def format(self, record):
        # Add color to the level name
        level_color = self.COLORS.get(record.levelname, '')
        record.colored_levelname = f"{level_color}{record.levelname}{Style.RESET_ALL}"

        # Format timestamp in a readable way
        record.formatted_time = datetime.fromtimestamp(record.created).strftime('%H:%M:%S')

        # Get the appropriate icon for this log level
        level_icon = self.ICONS.get(record.levelname, '📄')

        # Check if there's an operation-specific icon
        operation_icon = ''
        if hasattr(record, 'operation'):
            operation_icon = Operation.get_icon(record.operation)

        # Get the original message and add icon if not already present
        original_message = record.getMessage()

        # Use operation icon if available, otherwise use level icon
        icon_to_use = operation_icon if operation_icon else level_icon

        # Add icon if not already present in message
        if not any(emoji in original_message for emoji in [op.icon for op in Operation] + list(self.ICONS.values())):
            formatted_message = f"{icon_to_use} {original_message}"
        else:
            formatted_message = original_message

        # Create the formatted message
        if record.levelno >= logging.ERROR:
            # For errors, use red timestamp and bold message
            formatted = (
                f"{Fore.RED}{record.formatted_time}{Style.RESET_ALL} "
                f"│ {record.colored_levelname} "
                f"│ {Style.BRIGHT}{formatted_message}{Style.RESET_ALL}"
            )
        elif record.levelno >= logging.WARNING:
            # For warnings, use yellow timestamp
            formatted = (
                f"{Fore.YELLOW}{record.formatted_time}{Style.RESET_ALL} "
                f"│ {record.colored_levelname} "
                f"│ {formatted_message}"
            )
        else:
            # For info/debug, use normal colors
            formatted = (
                f"{Fore.BLUE}{record.formatted_time}{Style.RESET_ALL} "
                f"│ {record.colored_levelname} "
                f"│ {formatted_message}"
            )

        # Add exception info if present
        if record.exc_info:
            formatted += f"\n{Fore.RED}{'─' * 60}{Style.RESET_ALL}"
            formatted += f"\n{Fore.RED}{self.formatException(record.exc_info)}{Style.RESET_ALL}"
            formatted += f"\n{Fore.RED}{'─' * 60}{Style.RESET_ALL}"

        return formatted


class JSONFileFormatter(logging.Formatter):
    """Custom formatter for structured JSON file logging"""

    ICONS = {
        'DEBUG': '🔍',
        'INFO': '📝',
        'WARNING': '⚠️',
        'ERROR': '❌',
        'CRITICAL': '🚨',
    }

    def format(self, record):
        # Get the appropriate icon for this log level
        level_icon = self.ICONS.get(record.levelname, '📄')

        # Check if there's an operation-specific icon
        operation_icon = ''
        if hasattr(record, 'operation'):
            operation_icon = Operation.get_icon(record.operation)

        # Get the original message and add icon if not already present
        original_message = record.getMessage()

        # Use operation icon if available, otherwise use level icon
        icon_to_use = operation_icon if operation_icon else level_icon

        # Add icon if not already present in message
        if not any(emoji in original_message for emoji in [op.icon for op in Operation] + list(self.ICONS.values())):
            formatted_message = f"{icon_to_use} {original_message}"
        else:
            formatted_message = original_message

        # Create structured log entry
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'message': formatted_message,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'thread': record.thread,
            'process': record.process,
        }

        # Add request context if available
        if hasattr(record, 'request_id'):
            log_entry['request_id'] = record.request_id
        if hasattr(record, 'endpoint'):
            log_entry['endpoint'] = record.endpoint
        if hasattr(record, 'method'):
            log_entry['method'] = record.method
        if hasattr(record, 'user_agent'):
            log_entry['user_agent'] = record.user_agent

        # Add exception details if present
        if record.exc_info:
            log_entry['exception'] = {
                'type': record.exc_info[0].__name__,
                'message': str(record.exc_info[1]),
                'traceback': traceback.format_exception(*record.exc_info)
            }

        # Add extra fields if present
        if hasattr(record, 'extra_data'):
            log_entry['extra'] = record.extra_data

        return json.dumps(log_entry, ensure_ascii=False, separators=(',', ':'))


class CustomTimedRotatingFileHandler(TimedRotatingFileHandler):
    """Enhanced TimedRotatingFileHandler with custom naming and cleanup"""

    def __init__(self, base_filename: str, when: str = 'midnight', interval: int = 1,
                 backup_count: int = 7, encoding: str = 'utf-8'):
        self.base_filename = base_filename
        self.backup_count = backup_count

        # Ensure the log directory exists
        log_dir = Path(base_filename).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        # Generate today's filename
        today = datetime.now().strftime('%Y-%m-%d')
        filename = f"{base_filename}_{today}.log"

        super().__init__(filename, when, interval, backup_count, encoding=encoding)

        # Clean up old files on initialization
        self._cleanup_old_files()

    def doRollover(self):
        """Override to use custom filename format"""
        if self.stream:
            self.stream.close()
            self.stream = None

        # Generate new filename with date
        today = datetime.now().strftime('%Y-%m-%d')
        new_filename = f"{self.base_filename}_{today}.log"

        # Update the baseFilename
        self.baseFilename = new_filename

        # Open new file
        if not self.delay:
            self.stream = self._open()

        # Clean up old files
        self._cleanup_old_files()

    def _cleanup_old_files(self):
        """Remove log files older than backup_count days"""
        try:
            log_dir = Path(self.base_filename).parent
            base_name = Path(self.base_filename).stem

            # Calculate cutoff date
            cutoff_date = datetime.now() - timedelta(days=self.backup_count)

            # Find and remove old log files
            for log_file in log_dir.glob(f"{base_name}_*.log"):
                try:
                    # Extract date from filename
                    date_str = log_file.stem.split('_')[-1]
                    file_date = datetime.strptime(date_str, '%Y-%m-%d')

                    if file_date < cutoff_date:
                        log_file.unlink()
                        print(f"🗑️  Cleaned up old log file: {log_file.name}")
                except (ValueError, IndexError):
                    # Skip files that don't match our naming pattern
                    continue
        except Exception as e:
            print(f"⚠️  Error during log cleanup: {e}")


class ErrorLogger:
    """Main error logging class with enhanced features"""

    def __init__(self, name: str = "freefox", log_dir: str = "logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)

        # Prevent duplicate handlers
        if self.logger.handlers:
            self.logger.handlers.clear()

        self._setup_file_handler()
        self._setup_console_handler()

    def _setup_file_handler(self):
        """Setup file handler for error logging only"""
        file_path = self.log_dir / "errors"

        # Create custom rotating file handler
        file_handler = CustomTimedRotatingFileHandler(
            base_filename=str(file_path),
            when='midnight',
            interval=1,
            backup_count=7,
            encoding='utf-8'
        )

        # Only log errors and above to file
        file_handler.setLevel(logging.ERROR)
        file_handler.setFormatter(JSONFileFormatter())

        self.logger.addHandler(file_handler)

    def _setup_console_handler(self):
        """Setup console handler with colored output"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ColoredConsoleFormatter())

        self.logger.addHandler(console_handler)

    def error(self, message: str, exc_info: Optional[Exception] = None,
              extra_data: Optional[Dict[str, Any]] = None, **kwargs):
        """Log error with enhanced context"""
        extra = kwargs.copy()
        if extra_data:
            extra['extra_data'] = extra_data

        self.logger.error(message, exc_info=exc_info, extra=extra)

    def warning(self, message: str, **kwargs):
        """Log warning (console only)"""
        self.logger.warning(message, extra=kwargs)

    def info(self, message: str, **kwargs):
        """Log info (console only)"""
        self.logger.info(message, extra=kwargs)

    def debug(self, message: str, **kwargs):
        """Log debug (console only)"""
        self.logger.debug(message, extra=kwargs)


# Global logger instance
error_logger = ErrorLogger()


def log_error(message: str, exc: Optional[Exception] = None,
              request_context: Optional[Dict[str, Any]] = None,
              extra_data: Optional[Dict[str, Any]] = None,
              operation: Optional[Union[Operation, str]] = None):
    """
    Convenience function for logging errors with context

    Args:
        message: Error message
        exc: Exception object (optional)
        request_context: Request context (method, endpoint, etc.)
        extra_data: Additional data to log
        operation: Operation enum or string for specific icon (optional)
    """
    kwargs = {}

    if request_context:
        kwargs.update(request_context)

    # Add operation to kwargs so it can be used by formatters
    if operation:
        kwargs['operation'] = operation

    error_logger.error(
        message=message,
        exc_info=exc,
        extra_data=extra_data,
        **kwargs
    )


def log_info(message: str, operation: Optional[Union[Operation, str]] = None, **kwargs):
    """
    Convenience function for info logging

    Args:
        message: Info message
        operation: Operation enum or string for specific icon (optional)
        **kwargs: Additional context data
    """
    # Add operation to kwargs so it can be used by formatters
    if operation:
        kwargs['operation'] = operation

    error_logger.info(message, **kwargs)


def log_warning(message: str, operation: Optional[Union[Operation, str]] = None, **kwargs):
    """
    Convenience function for warning logging

    Args:
        message: Warning message
        operation: Operation enum or string for specific icon (optional)
        **kwargs: Additional context data
    """
    # Add operation to kwargs so it can be used by formatters
    if operation:
        kwargs['operation'] = operation

    error_logger.warning(message, **kwargs)


def log_error_with_context(message: str, exc: Optional[Exception] = None,
                          extra_data: Optional[Dict[str, Any]] = None,
                          operation: Optional[Union[Operation, str]] = None):
    """
    Log an error with the current request context

    Args:
        message: Error message
        exc: Exception object (optional)
        extra_data: Additional data to include in the log
        operation: Operation enum or string for specific icon (optional)
    """
    # Import here to avoid circular imports
    from app.middleware.logging_middleware import get_request_context

    context = get_request_context()
    log_error(
        message=message,
        exc=exc,
        request_context=context,
        extra_data=extra_data,
        operation=operation
    )
