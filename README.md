# FreeFox - PDF Utilities

A web application that provides various PDF utilities including:

- Merge PDF
- Compress PDF
- Word to PDF conversion

## Features

- **Modern UI**: Built with Next.js and Shadcn UI components
- **Drag-and-drop**: Easy file upload with drag-and-drop functionality
- **Real-time processing**: See the status of your file processing in real-time
- **High performance**: Designed to handle up to 1000 simultaneous users
- **No login required**: Use the tools without creating an account
- **No data storage**: Files are processed on-the-fly and not stored permanently

## Tech Stack

### Frontend

- Next.js 14
- TypeScript
- Tailwind CSS
- Shadcn UI
- React Dropzone

### Backend

- Python
- FastAPI
- PyPDF2
- ReportLab

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Python (v3.8 or later)
- npm or yarn

### Installation

1. Clone the repository:

```
git clone https://github.com/yourusername/freefox.git
cd freefox
```

2. Install frontend dependencies:

```
cd frontend
npm install
```

3. Install backend dependencies:

```
cd ../backend
pip install -r requirements.txt
```

### Running the Application

You can run both the frontend and backend servers with a single command:

```
run_app.bat
```

Or run them separately:

#### Frontend

```
cd frontend
npm run dev
```

#### Backend

```
cd backend
python run.py
```

The frontend will be available at http://localhost:3001
The backend API will be available at http://localhost:8001

## API Endpoints

- `POST /merge-pdf`: Merge multiple PDF files into one
- `POST /compress-pdf`: Compress a PDF file to reduce its size
- `POST /word-to-pdf`: Convert a Word document to PDF format

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgements

- [Next.js](https://nextjs.org/)
- [Shadcn UI](https://ui.shadcn.com/)
- [FastAPI](https://fastapi.tiangolo.com/)
- [PyPDF2](https://pypdf2.readthedocs.io/)
- [ReportLab](https://www.reportlab.com/)
