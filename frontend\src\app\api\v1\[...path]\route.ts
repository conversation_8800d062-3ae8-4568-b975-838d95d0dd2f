/* eslint-disable @typescript-eslint/no-unused-vars */
import { NextRequest, NextResponse } from "next/server";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8001";

/**
 * Helper function to handle API responses
 * Handles different response types (JSON, binary files, etc.)
 */
async function handleResponse(response: Response) {
  // For binary responses like PDFs, we need to handle them differently
  const contentDisposition = response.headers.get("Content-Disposition");
  const responseContentType = response.headers.get("Content-Type") || "";

  if (
    responseContentType.includes("application/pdf") ||
    responseContentType.includes("application/zip") ||
    contentDisposition?.includes("attachment")
  ) {
    const blob = await response.blob();

    const newHeaders = new Headers();

    response.headers.forEach((value, key) => {
      newHeaders.set(key, value);
    });

    newHeaders.set("Content-Type", responseContentType);
    if (contentDisposition) {
      newHeaders.set("Content-Disposition", contentDisposition);
    }

    const compressionRatio = response.headers.get("X-Compression-Ratio");
    const originalSize = response.headers.get("X-Original-Size");
    const compressedSize = response.headers.get("X-Compressed-Size");

    if (compressionRatio)
      newHeaders.set("X-Compression-Ratio", compressionRatio);
    if (originalSize) newHeaders.set("X-Original-Size", originalSize);
    if (compressedSize) newHeaders.set("X-Compressed-Size", compressedSize);

    newHeaders.set(
      "Access-Control-Expose-Headers",
      "Content-Disposition, X-Compression-Ratio, X-Original-Size, X-Compressed-Size"
    );

    const newResponse = new NextResponse(blob, {
      status: response.status,
      headers: newHeaders,
    });

    return newResponse;
  }

  // For JSON responses
  try {
    const data = await response.json();
    return NextResponse.json(data, { status: response.status });
  } catch (e) {
    // If not JSON, return the raw response
    const text = await response.text();
    return new NextResponse(text, {
      status: response.status,
      headers: {
        "Content-Type": responseContentType,
      },
    });
  }
}

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ path: string[] }> }
) {
  // Await the params to get the actual values
  const params = await context.params;

  // Extract the path directly from the URL to avoid the dynamic params issue
  const url = new URL(request.url);
  const path = url.pathname.replace("/api/v1/", "");
  const queryString = url.search;

  try {
    // Add API version prefix to the path if it's not already there
    const apiPath = path.startsWith("api/v1/") ? path : `api/v1/${path}`;

    console.log(
      `Forwarding GET request to: ${API_BASE_URL}/${apiPath}${queryString}`
    );

    const response = await fetch(`${API_BASE_URL}/${apiPath}${queryString}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    // Process the response using our helper function
    return handleResponse(response);
  } catch (error) {
    console.error("API proxy error:", error);
    return NextResponse.json(
      { error: "Failed to fetch data from API" },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  context: { params: Promise<{ path: string[] }> }
) {
  // Await the params to get the actual values
  const params = await context.params;

  // Extract the path directly from the URL to avoid the dynamic params issue
  const url = new URL(request.url);
  const path = url.pathname.replace("/api/v1/", "");

  try {
    // Clone the request to forward it
    const contentType = request.headers.get("Content-Type") || "";

    // Get the request body based on content type
    let body;
    let response;

    if (contentType.includes("multipart/form-data")) {
      // For multipart/form-data, we need to get the formData and pass it directly
      body = await request.formData();

      // Add API version prefix to the path if it's not already there
      const apiPath = path.startsWith("api/v1/") ? path : `api/v1/${path}`;

      console.log(`Forwarding FormData request to: ${API_BASE_URL}/${apiPath}`);

      // Forward the request to the backend API
      // Don't set Content-Type for FormData, let fetch set it with the correct boundary
      response = await fetch(`${API_BASE_URL}/${apiPath}`, {
        method: "POST",
        body: body,
      });
    } else {
      // For other content types
      body = await request.text();

      // Add API version prefix to the path if it's not already there
      const apiPath = path.startsWith("api/v1/") ? path : `api/v1/${path}`;

      console.log(`Forwarding JSON request to: ${API_BASE_URL}/${apiPath}`);

      // Forward the request to the backend API
      response = await fetch(`${API_BASE_URL}/${apiPath}`, {
        method: "POST",
        headers: {
          "Content-Type": contentType,
        },
        body: body,
      });
    }

    // Process the response using our helper function
    return handleResponse(response);
  } catch (error) {
    console.error("API proxy error:", error);
    return NextResponse.json(
      { error: "Failed to send data to API" },
      { status: 500 }
    );
  }
}
