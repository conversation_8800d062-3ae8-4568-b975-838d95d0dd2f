"use client";

import { AffiliateLink, AffiliateSection } from "@/components/AffiliateSection";
import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { Success<PERSON>on<PERSON>tti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const pdfRotateFAQs = [
  {
    question: "What rotation angles are supported?",
    answer:
      "You can rotate PDF pages by 90° clockwise, 180°, or 270° (90° counter-clockwise). These cover all common rotation needs for correcting document orientation.",
  },
  {
    question: "Can I rotate multiple PDF files at once?",
    answer:
      "Yes! You can upload up to 5 PDF files and rotate them all with the same angle. The rotated files will be packaged in a ZIP archive for easy download.",
  },
  {
    question: "Will rotating a PDF affect its quality?",
    answer:
      "No, PDF rotation is a lossless operation. The content, text, images, and formatting remain exactly the same - only the page orientation changes.",
  },
  {
    question: "Can I rotate specific pages instead of the entire document?",
    answer:
      "Currently, the rotation applies to all pages in the PDF. If you need to rotate specific pages, you can first split the PDF, rotate the needed pages, and then merge them back.",
  },
  {
    question: "What happens to bookmarks and links after rotation?",
    answer:
      "Bookmarks and internal links are preserved and automatically adjusted to work correctly with the new page orientation.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "PDF Rotator",
  description:
    "Free online PDF rotation tool to change page orientation by 90°, 180°, or 270°",
  url: "https://freefox.com/rotate-pdf",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Rotate PDF pages",
    "Multiple rotation angles",
    "Batch processing",
    "Lossless rotation",
  ],
};

export default function RotatePDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [rotationAngle, setRotationAngle] = useState<90 | 180 | 270>(90);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);
  const [showSuccessState, setShowSuccessState] = useState(false);

  // Set page metadata on client side
  useEffect(() => {
    document.title = "Free PDF Rotator Online - Rotate PDF Pages | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Rotate PDF pages online for free. Change page orientation by 90°, 180°, or 270°. No registration required. Fast, secure, and easy to use."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/rotate-pdf");

    const fetchAffiliateLinks = async () => {
      try {
        // Fetch the JSON file from the public directory
        const response = await fetch("/data/pdf-rotate-affiliate.json");
        const data = await response.json();
        // Set the appropriate data into state
        setAffiliateLinks(data);
      } catch (error) {
        console.error("Failed to fetch affiliate links:", error);
      }
    };

    fetchAffiliateLinks();
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleStartOver = () => {
    setFiles([]);
    setShowSuccessState(false);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
  };

  const handleRotatePDF = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one PDF file");
      return;
    }

    setIsProcessing(true);
    setProgress(0);

    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });
      formData.append("rotation_angle", rotationAngle.toString());

      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 90) {
            return 90;
          }
          return prev + 5;
        });
      }, 200);

      const response = await apiPost("rotate-pdf", formData);

      clearInterval(progressInterval);

      setProgress(100);

      const defaultFilename =
        files.length > 1 ? "rotated_pdfs.zip" : `rotated_${files[0].name}`;
      await downloadFromResponse(response, defaultFilename);

      setShowConfetti(true);
      setShowSuccessState(true);
    } catch (error) {
      console.error("Error rotating PDF:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to rotate PDF"
      );
    } finally {
      setIsProcessing(false);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.08,
        delayChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 10, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 500, damping: 25 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div className="mb-8 space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free PDF Rotator Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Rotate PDF pages by 90°, 180°, or 270°. Fix document orientation
                instantly. No registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Multiple Angles
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Batch Processing
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Lossless Quality
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{ "application/pdf": [".pdf"] }}
              maxFiles={5}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              showSuccessState={showSuccessState}
              successMessage="PDF rotated successfully and downloaded!"
              successButtonText="Rotate More PDFs"
              onStartOver={handleStartOver}
            />

            {files.length > 0 && !showSuccessState && (
              <div className="bg-muted/30 rounded-lg space-y-4">
                <h3 className="font-medium font-display">Rotation Options</h3>

                <div className="grid grid-cols-3 gap-4">
                  <div
                    className={`border rounded-lg p-4 text-center cursor-pointer transition-all ${
                      rotationAngle === 90
                        ? "border-primary bg-primary/5"
                        : "hover:border-primary/50"
                    }`}
                    onClick={() => setRotationAngle(90)}
                  >
                    <div className="flex justify-center mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        x="0px"
                        y="0px"
                        width="24"
                        height="24"
                        viewBox="0 0 512 512"
                        enableBackground="new 0 0 512 512"
                      >
                        <path
                          style={{ fill: "#B3404A" }}
                          d="M256,415.494c-64.27,0-116.555-52.287-116.555-116.555c0-7.824,6.342-14.166,14.166-14.166
	c7.824,0,14.166,6.342,14.166,14.166c0,48.646,39.577,88.223,88.223,88.223s88.223-39.577,88.223-88.223
	c0-7.824,6.342-14.166,14.166-14.166s14.166,6.342,14.166,14.166C372.555,363.209,320.268,415.494,256,415.494z"
                        />
                        <path
                          style={{ fill: "#F4B2B0" }}
                          d="M298.909,157.28l-65.114-63.357v-70.15c0-8.524,10.292-12.81,16.342-6.805L391.531,157.28
	L250.137,297.593c-6.05,6.005-16.342,1.718-16.342-6.805v-70.15L298.909,157.28z"
                        />
                        <path
                          style={{ fill: "#B3404A" }}
                          d="M454.894,284.774c-7.824,0-14.166,6.342-14.166,14.166c0,101.859-82.869,184.728-184.728,184.728
	S71.272,400.799,71.272,298.94c0-94.935,71.989-173.354,164.241-183.581l43.083,41.921l-54.679,53.204
	c-2.741,2.666-4.287,6.328-4.287,10.153v70.15c0,13.108,10.687,23.773,23.821,23.773c6.254,0,12.173-2.455,16.665-6.912
	l141.395-140.312c2.68-2.659,4.187-6.28,4.187-10.055c0-3.775-1.507-7.396-4.187-10.055L260.117,6.912
	C255.625,2.455,249.706,0,243.452,0c-13.136,0-23.823,10.664-23.823,23.773v65.23C119.415,106.313,42.94,193.855,42.94,298.94
	C42.94,416.422,138.518,512,256,512s213.06-95.578,213.06-213.06C469.06,291.116,462.718,284.774,454.894,284.774z M247.962,34.766
	L371.421,157.28L247.962,279.795v-53.176l60.826-59.185c2.741-2.666,4.287-6.328,4.287-10.153s-1.546-7.487-4.287-10.153
	l-60.826-59.185L247.962,34.766L247.962,34.766z"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-medium">90° Clockwise</span>
                  </div>

                  <div
                    className={`border rounded-lg p-4 text-center cursor-pointer transition-all ${
                      rotationAngle === 180
                        ? "border-primary bg-primary/5"
                        : "hover:border-primary/50"
                    }`}
                    onClick={() => setRotationAngle(180)}
                  >
                    <div className="flex justify-center mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        x="0px"
                        y="0px"
                        width="24"
                        height="24"
                        viewBox="0 0 512 512"
                        enableBackground="new 0 0 512 512"
                      >
                        <path
                          style={{ fill: "#F4B2B0" }}
                          d="M116.532,215.7H395.47c36.816,0,57.855,42.009,35.803,71.491L286.019,481.379
	c-14.993,20.043-45.041,20.043-60.034,0L80.729,287.191C58.677,257.709,79.714,215.7,116.532,215.7z"
                        />
                        <path
                          style={{ fill: "#B3404A" }}
                          d="M449.386,233.411c-10.286-20.54-30.946-33.298-53.916-33.298h-66.552
	C326.773,89.402,236.06,0,124.847,0c-8.61,0-15.589,6.979-15.589,15.589v114.759c0,8.61,6.979,15.589,15.589,15.589
	c14.01,0,27.556,5.074,38.143,14.29c6.496,5.654,16.342,4.97,21.993-1.525c5.653-6.494,4.97-16.34-1.525-21.993
	c-12.293-10.699-27.185-17.765-43.024-20.577V31.875c86.772,7.791,155.289,79.949,157.307,168.237H116.53
	c-22.97,0-43.63,12.76-53.916,33.298s-8.128,44.725,5.631,63.119l145.254,194.188c10.117,13.526,25.608,21.284,42.5,21.284
	s32.383-7.759,42.5-21.284l145.254-194.188C457.514,278.135,459.671,253.949,449.386,233.411z M418.789,277.855L273.535,472.043
	c-4.173,5.581-10.565,8.781-17.534,8.781c-6.97,0-13.36-3.2-17.534-8.781L93.212,277.855c-6.745-9.018-7.762-20.414-2.72-30.484
	c5.041-10.07,14.777-16.081,26.038-16.081h278.94c11.261,0,20.995,6.011,26.038,16.081
	C426.551,257.441,425.535,268.837,418.789,277.855z"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-medium">180°</span>
                  </div>

                  <div
                    className={`border rounded-lg p-4 text-center cursor-pointer transition-all ${
                      rotationAngle === 270
                        ? "border-primary bg-primary/5"
                        : "hover:border-primary/50"
                    }`}
                    onClick={() => setRotationAngle(270)}
                  >
                    <div className="flex justify-center mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        x="0px"
                        y="0px"
                        width="24"
                        height="24"
                        viewBox="0 0 512 512"
                        enableBackground="new 0 0 512 512"
                      >
                        <path
                          style={{ fill: "#B3404A" }}
                          d="M256,415.494c-64.27,0-116.555-52.287-116.555-116.555c0-7.824,6.342-14.166,14.166-14.166
 c7.824,0,14.166,6.342,14.166,14.166c0,48.646,39.577,88.223,88.223,88.223s88.223-39.577,88.223-88.223
 c0-7.824,6.342-14.166,14.166-14.166s14.166,6.342,14.166,14.166C372.555,363.209,320.268,415.494,256,415.494z"
                        />
                        <path
                          style={{ fill: "#F4B2B0" }}
                          d="M213.09,157.28l65.114-63.357v-70.15c0-8.524-10.292-12.81-16.342-6.805L120.469,157.28
 l141.393,140.312c6.05,6.005,16.342,1.718,16.342-6.805v-70.15L213.09,157.28z"
                        />
                        <path
                          style={{ fill: "#B3404A" }}
                          d="M292.37,88.993v-65.22C292.37,10.664,281.683,0,268.547,0c-6.254,0-12.171,2.455-16.663,6.912
 L110.489,147.224c-2.68,2.659-4.187,6.28-4.187,10.055s1.507,7.396,4.187,10.055l141.395,140.312
 c4.491,4.457,10.409,6.912,16.663,6.912c0,0,0,0,0.001,0c6.089,0,11.894-2.292,16.348-6.451c4.821-4.503,7.474-10.654,7.474-17.322
 v-70.15c0-3.825-1.546-7.487-4.287-10.153l-54.679-53.204l43.086-41.923c92.252,10.226,164.239,88.648,164.239,183.582
 c0,101.859-82.869,184.728-184.728,184.728S71.272,400.799,71.272,298.94c0-7.824-6.342-14.166-14.166-14.166
 S42.94,291.116,42.94,298.94C42.94,416.422,138.518,512,256,512s213.06-95.578,213.06-213.06
 C469.06,193.854,392.587,106.301,292.37,88.993z M264.038,87.942l-60.826,59.185c-2.741,2.666-4.287,6.328-4.287,10.153
 s1.546,7.487,4.287,10.153l60.826,59.185v53.176L140.579,157.28L264.038,34.766V87.942z"
                        />
                      </svg>
                    </div>
                    <span className="text-sm font-medium">
                      90° Counter-Clockwise
                    </span>
                  </div>
                </div>
              </div>
            )}

            {files.length > 0 && !showSuccessState && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleRotatePDF}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Processing..." : "Rotate PDF"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          {/* Affiliate Section */}
          <motion.div className="mt-6" variants={itemVariants}>
            <AffiliateSection
              title="🍃 Rotate PDF Pages the Right Way!"
              subtitle="Easily rotate PDF pages permanently with ease"
              links={affiliateLinks}
            />
          </motion.div>

          <motion.div
            className="mt-6 bg-muted/50 p-6 rounded-lg"
            variants={itemVariants}
          >
            <h2 className="text-xl font-semibold mb-4 font-display">
              About PDF Rotation
            </h2>
            <div className="space-y-4 text-sm text-muted-foreground">
              <p>
                Our PDF rotation tool allows you to change the orientation of
                pages in your PDF documents. This is useful when you have
                scanned documents or PDFs with pages that are upside down or
                sideways.
              </p>
              <p>
                You can choose to rotate pages by 90°, 180°, or 270° degrees.
                The tool supports batch processing, allowing you to rotate
                multiple PDF files at once with the same rotation angle. For
                multiple files, the results will be provided as a zip file
                containing all the rotated PDFs.
              </p>
              <p>
                <strong>Note:</strong> Your files are processed securely on our
                servers and are not stored permanently. They are automatically
                deleted after processing.
              </p>
            </div>
          </motion.div>

          {/* FAQ Section */}
          <motion.div variants={itemVariants}>
            <SEOFaq
              title="PDF Rotation FAQ"
              faqs={pdfRotateFAQs}
              toolName="PDF Rotator"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
