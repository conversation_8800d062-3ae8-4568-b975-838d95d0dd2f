"""
Utility functions for converting PDF documents to Word.
"""
import os

from app.utils.error_logger import Operation, log_error, log_info


def convert_pdf_to_word(input_path, output_path):
    """
    Convert a PDF document to Word format using pdf2docx.
    
    Args:
        input_path (str): Path to the input PDF document
        output_path (str): Path to save the output Word document
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        from pdf2docx import Converter
        
        log_info(f"Converting {input_path} to {output_path} using pdf2docx")
        cv = Converter(input_path)
        cv.convert(output_path)
        cv.close()
        
        return os.path.exists(output_path)
    except Exception as e:
        log_error(f"PDF to Word conversion failed: {str(e)}", exc=e, operation=Operation.PDF_CONVERT)
        return False