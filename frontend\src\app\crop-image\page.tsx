"use client";

import { AffiliateLink, AffiliateSection } from "@/components/AffiliateSection";
import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { SuccessConfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";

const imageCropFAQs = [
  {
    question: "What image formats can I crop?",
    answer:
      "You can crop JPG, PNG, WebP, GIF, BMP, and TIFF images. All formats are supported with precise cropping capabilities.",
  },
  {
    question: "How do I select the crop area?",
    answer:
      "Click and drag on the image preview to select the area you want to keep, or enter exact coordinates in the input fields below the preview.",
  },
  {
    question: "Will cropping affect image quality?",
    answer:
      "No, cropping only removes unwanted parts of the image without affecting the quality of the remaining area. The cropped portion maintains its original resolution.",
  },
  {
    question: "Can I crop to specific aspect ratios?",
    answer:
      "Yes, you can manually adjust the coordinates to achieve specific aspect ratios like 16:9, 4:3, or 1:1 for social media posts.",
  },
  {
    question: "Is there a minimum crop size?",
    answer:
      "The minimum crop size is 1x1 pixel, but for practical use, we recommend keeping at least 50x50 pixels for visible results.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Image Cropper",
  description:
    "Free online image cropper to crop and trim images with precision",
  url: "https://freefox.com/crop-image",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Crop images",
    "Interactive selection",
    "Precise coordinates",
    "Multiple formats",
  ],
};

export default function CropImagePage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [left, setLeft] = useState(0);
  const [top, setTop] = useState(0);
  const [right, setRight] = useState(0);
  const [bottom, setBottom] = useState(0);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageDimensions, setImageDimensions] = useState({
    width: 0,
    height: 0,
  });
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [cropBox, setCropBox] = useState({ x: 0, y: 0, width: 0, height: 0 });
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);
  const [showSuccessState, setShowSuccessState] = useState(false);

  useEffect(() => {
    document.title = "Free Image Cropper Online - Crop Images | FreeFox";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Crop images online for free. Precise cropping with interactive selection or manual coordinates. Support for JPG, PNG, WebP and more. No registration required."
      );
    }
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/crop-image");

    const fetchAffiliateLinks = async () => {
      try {
        // Fetch the JSON file from the public directory
        const response = await fetch("/data/image-crop-affiliate.json");
        const data = await response.json();
        // Set the appropriate data into state
        setAffiliateLinks(data);
      } catch (error) {
        console.error("Failed to fetch affiliate links:", error);
      }
    };

    fetchAffiliateLinks();
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    // With replaceExisting=true, we'll always get just the latest file
    setFiles(selectedFiles);

    if (selectedFiles.length > 0) {
      // Create a preview of the image
      const file = selectedFiles[0];
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImagePreview(e.target.result as string);

          // Get image dimensions
          const img = new Image();
          img.onload = () => {
            setImageDimensions({ width: img.width, height: img.height });
            // Initialize crop box to full image
            setCropBox({ x: 0, y: 0, width: img.width, height: img.height });
            setLeft(0);
            setTop(0);
            setRight(img.width);
            setBottom(img.height);
          };
          img.src = e.target.result as string;
        }
      };
      reader.readAsDataURL(file);
    } else {
      setImagePreview(null);
      setImageDimensions({ width: 0, height: 0 });
    }
  };

  // Draw the image and crop box on the canvas
  useEffect(() => {
    if (!imagePreview || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const img = new Image();
    img.onload = () => {
      // Set canvas size to match image
      canvas.width = img.width;
      canvas.height = img.height;

      // Draw image
      ctx.drawImage(img, 0, 0);

      // Draw semi-transparent overlay
      ctx.fillStyle = "rgba(0, 0, 0, 0.5)";
      ctx.fillRect(0, 0, canvas.width, canvas.height);

      // Clear the crop area
      ctx.clearRect(cropBox.x, cropBox.y, cropBox.width, cropBox.height);

      // Draw crop box border
      ctx.strokeStyle = "rgba(var(--primary-rgb), 1)";
      ctx.lineWidth = 2;
      ctx.strokeRect(cropBox.x, cropBox.y, cropBox.width, cropBox.height);
    };
    img.src = imagePreview;
  }, [imagePreview, cropBox]);

  // Update form inputs when crop box changes
  useEffect(() => {
    setLeft(Math.round(cropBox.x));
    setTop(Math.round(cropBox.y));
    setRight(Math.round(cropBox.x + cropBox.width));
    setBottom(Math.round(cropBox.y + cropBox.height));
  }, [cropBox]);

  // Handle mouse events for interactive cropping
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    setIsDragging(true);
    setDragStart({ x, y });
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDragging || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    // Calculate new crop box
    const newX = Math.min(dragStart.x, x);
    const newY = Math.min(dragStart.y, y);
    const newWidth = Math.abs(x - dragStart.x);
    const newHeight = Math.abs(y - dragStart.y);

    // Update crop box
    setCropBox({
      x: newX,
      y: newY,
      width: newWidth,
      height: newHeight,
    });
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Update crop box when form inputs change
  const updateCropBoxFromInputs = () => {
    if (left >= 0 && top >= 0 && right > left && bottom > top) {
      setCropBox({
        x: left,
        y: top,
        width: right - left,
        height: bottom - top,
      });
    }
  };

  const handleStartOver = () => {
    setFiles([]);
    setShowSuccessState(false);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
  };

  const handleCrop = async () => {
    if (files.length === 0) {
      toast.error("Please select an image file to crop");
      return;
    }

    if (left < 0 || top < 0 || left >= right || top >= bottom) {
      toast.error("Invalid crop coordinates");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append("file", files[0]);
      formData.append("left", left.toString());
      formData.append("top", top.toString());
      formData.append("right", right.toString());
      formData.append("bottom", bottom.toString());

      // Simulate progress
      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 5;
        });
      }, 300);

      setProgress(30);

      const response = await apiPost("crop-image", formData);

      setProgress(90);

      await downloadFromResponse(response, `cropped_${files[0].name}`);

      setProgress(100);
      setShowConfetti(true);
      setShowSuccessState(true);
    } catch (error) {
      console.error("Error cropping image:", error);
      toast.error("Failed to crop image. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto space-y-8"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.div className="text-center space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free Image Cropper Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Crop images with precision using interactive selection or manual
                coordinates. No registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground justify-center">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Interactive Cropping
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Precise Coordinates
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Multiple Formats
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{
                "image/jpeg": [".jpg", ".jpeg"],
                "image/png": [".png"],
                "image/webp": [".webp"],
                "image/gif": [".gif"],
                "image/bmp": [".bmp"],
                "image/tiff": [".tiff", ".tif"],
              }}
              maxFiles={1}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              showSuccessState={showSuccessState}
              successMessage="Image cropped successfully and downloaded!"
              successButtonText="Crop Another Image"
              onStartOver={handleStartOver}
            />

            {imagePreview && !showSuccessState && (
              <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                <h3 className="font-medium font-display">Crop Area</h3>

                <div className="relative overflow-hidden rounded-md border border-muted">
                  <canvas
                    ref={canvasRef}
                    className="max-w-full h-auto cursor-crosshair"
                    onMouseDown={handleMouseDown}
                    onMouseMove={handleMouseMove}
                    onMouseUp={handleMouseUp}
                    onMouseLeave={handleMouseUp}
                  />
                </div>

                <p className="text-xs text-muted-foreground">
                  Click and drag on the image to select the crop area, or enter
                  coordinates manually below.
                </p>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="left">Left (X)</Label>
                    <Input
                      id="left"
                      type="number"
                      min="0"
                      max={imageDimensions.width}
                      value={left}
                      onChange={(e) => {
                        setLeft(parseInt(e.target.value) || 0);
                        updateCropBoxFromInputs();
                      }}
                      disabled={isProcessing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="top">Top (Y)</Label>
                    <Input
                      id="top"
                      type="number"
                      min="0"
                      max={imageDimensions.height}
                      value={top}
                      onChange={(e) => {
                        setTop(parseInt(e.target.value) || 0);
                        updateCropBoxFromInputs();
                      }}
                      disabled={isProcessing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="right">Right (X)</Label>
                    <Input
                      id="right"
                      type="number"
                      min={left + 1}
                      max={imageDimensions.width}
                      value={right}
                      onChange={(e) => {
                        setRight(parseInt(e.target.value) || 0);
                        updateCropBoxFromInputs();
                      }}
                      disabled={isProcessing}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="bottom">Bottom (Y)</Label>
                    <Input
                      id="bottom"
                      type="number"
                      min={top + 1}
                      max={imageDimensions.height}
                      value={bottom}
                      onChange={(e) => {
                        setBottom(parseInt(e.target.value) || 0);
                        updateCropBoxFromInputs();
                      }}
                      disabled={isProcessing}
                    />
                  </div>
                </div>
              </div>
            )}
            {files.length > 0 && !showSuccessState && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleCrop}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Cropping..." : "Crop Image"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          <motion.div className="mt-6" variants={itemVariants}>
            <AffiliateSection
              title="🔥 Crop Images Precisely & Instantly!"
              subtitle="Use top tools to crop, trim, and frame your images exactly how you want"
              links={affiliateLinks}
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <SEOFaq
              title="Image Cropping FAQ"
              faqs={imageCropFAQs}
              toolName="Image Cropper"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
