"use client";

import { AffiliateLink, AffiliateSection } from "@/components/AffiliateSection";
import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { Success<PERSON>onfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const imageToPdfFAQs = [
  {
    question: "What image formats can I convert to PDF?",
    answer:
      "You can convert JPG, PNG, WebP, GIF, BMP, and TIFF images to PDF. All common image formats are supported.",
  },
  {
    question: "Can I convert multiple images to one PDF?",
    answer:
      "Yes! You can upload up to 10 images and they will be combined into a single PDF document, with each image on a separate page.",
  },
  {
    question: "What page sizes are available?",
    answer:
      "We support A4 (210 × 297 mm) and Letter (8.5 × 11 in) page sizes. Images can be fitted to the page or placed at original size.",
  },
  {
    question: "Will the image quality be preserved?",
    answer:
      "Yes, the conversion process preserves the original quality of your images. High-quality images will result in high-quality PDFs.",
  },
  {
    question: "In what order are images arranged in the PDF?",
    answer:
      "Images are arranged in the order they were uploaded. Each image appears on a separate page in the final PDF document.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Image to PDF Converter",
  description:
    "Free online tool to convert images to PDF documents with custom page settings",
  url: "https://freefox.com/image-to-pdf",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Convert images to PDF",
    "Multiple images support",
    "Custom page sizes",
    "Preserve quality",
  ],
};

export default function ImageToPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [pageSize, setPageSize] = useState("A4");
  const [fitToPage, setFitToPage] = useState(true);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);
  const [showSuccessState, setShowSuccessState] = useState(false);

  useEffect(() => {
    document.title =
      "Free Image to PDF Converter Online - Convert Images to PDF | FreeFox";
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Convert images to PDF online for free. Combine multiple images into one PDF document. Support for JPG, PNG, WebP and more. No registration required."
      );
    }
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/image-to-pdf");

    const fetchAffiliateLinks = async () => {
      try {
        // Fetch the JSON file from the public directory
        const response = await fetch("/data/image-to-pdf-affiliate.json");
        const data = await response.json();
        // Set the appropriate data into state
        setAffiliateLinks(data);
      } catch (error) {
        console.error("Failed to fetch affiliate links:", error);
      }
    };

    fetchAffiliateLinks();
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleStartOver = () => {
    setFiles([]);
    setShowSuccessState(false);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select at least one image file to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });
      formData.append("page_size", pageSize);
      formData.append("fit_to_page", fitToPage.toString());

      const progressInterval = setInterval(() => {
        setProgress((prev) => {
          if (prev >= 80) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 4;
        });
      }, 300);

      setProgress(30);

      const response = await apiPost("image-to-pdf", formData);

      setProgress(90);

      let outputFilename = "images.pdf";
      if (files.length === 1) {
        const baseName =
          files[0].name.substring(0, files[0].name.lastIndexOf(".")) ||
          files[0].name;
        outputFilename = `${baseName}.pdf`;
      }

      await downloadFromResponse(response, outputFilename);

      setProgress(100);
      setShowConfetti(true);
      setShowSuccessState(true);
    } catch (error) {
      console.error("Error converting images to PDF:", error);
      toast.error("Failed to convert images to PDF. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 400, damping: 30 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto space-y-8"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          <motion.div className="text-center space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free Image to PDF Converter Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Convert images to PDF documents. Combine multiple images into
                one PDF. No registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground justify-center">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Multiple Images
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Custom Page Size
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Preserve Quality
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{
                "image/jpeg": [".jpg", ".jpeg"],
                "image/png": [".png"],
                "image/webp": [".webp"],
                "image/gif": [".gif"],
                "image/bmp": [".bmp"],
                "image/tiff": [".tiff", ".tif"],
              }}
              maxFiles={10}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              showSuccessState={showSuccessState}
              successMessage="Image(s) converted successfully and downloaded!"
              successButtonText="Convert More Images"
              onStartOver={handleStartOver}
            />

            {files.length > 0 && !showSuccessState && (
              <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                <h3 className="font-medium font-display">PDF Options</h3>
                <div className="space-y-4">
                  <div className="flex flex-col space-y-1">
                    <Label className="text-sm font-medium">Page Size</Label>
                    <Select
                      value={pageSize}
                      onValueChange={setPageSize}
                      disabled={isProcessing}
                    >
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="Select page size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="A4">A4 (210 × 297 mm)</SelectItem>
                        <SelectItem value="Letter">
                          Letter (8.5 × 11 in)
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="fit-to-page"
                      checked={fitToPage}
                      onCheckedChange={(checked) =>
                        setFitToPage(checked === true)
                      }
                      disabled={isProcessing}
                    />
                    <Label
                      htmlFor="fit-to-page"
                      className="text-sm font-normal"
                    >
                      Fit images to page
                    </Label>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    When enabled, images will be scaled to fit within the page
                    while maintaining their aspect ratio. When disabled, images
                    will be placed at their original size (may be cropped if
                    larger than the page).
                  </p>
                </div>
              </div>
            )}
            {files.length > 0 && !showSuccessState && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleConvert}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Converting..." : "Convert to PDF"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          <motion.div className="mt-6" variants={itemVariants}>
            <AffiliateSection
              title="🔥 Convert Images to PDF Instantly!"
              subtitle="Perfect for reports, portfolios, or preserving your photos as PDFs"
              links={affiliateLinks}
            />
          </motion.div>

          <motion.div variants={itemVariants}>
            <SEOFaq
              title="Image to PDF Conversion FAQ"
              faqs={imageToPdfFAQs}
              toolName="Image to PDF Converter"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
