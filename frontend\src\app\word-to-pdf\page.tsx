"use client";

import { AffiliateLink, AffiliateSection } from "@/components/AffiliateSection";
import { EnhancedFileUpload } from "@/components/EnhancedFileUpload";
import { MainLayout } from "@/components/MainLayout";
import { SEOFaq } from "@/components/SEOFaq";
import { Success<PERSON>onfetti } from "@/components/SuccessConfetti";
import { Button } from "@/components/ui/button";
import { apiPost, downloadFromResponse } from "@/lib/api";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const wordToPdfFAQs = [
  {
    question: "What Word formats can I convert to PDF?",
    answer:
      "You can convert both .doc (Word 97-2003) and .docx (Word 2007 and later) files to PDF. Our converter supports all modern Word document formats.",
  },
  {
    question: "Will the formatting be preserved in the PDF?",
    answer:
      "Yes! Our converter maintains all formatting including fonts, images, tables, headers, footers, and page layouts. The PDF will look exactly like your original Word document.",
  },
  {
    question: "Is there a file size limit for Word documents?",
    answer:
      "No, there are no file size limits. You can convert large Word documents with many pages, images, and complex formatting without restrictions.",
  },
  {
    question: "Can I convert password-protected Word documents?",
    answer:
      "Currently, password-protected Word documents need to be unlocked before conversion. Please remove the password protection and try again.",
  },
  {
    question: "Why convert Word to PDF?",
    answer:
      "PDF files maintain consistent formatting across all devices and platforms, cannot be easily edited, and are perfect for sharing final documents, resumes, reports, and official documents.",
  },
];

const jsonLd = {
  "@context": "https://schema.org",
  "@type": "WebApplication",
  name: "Word to PDF Converter",
  description:
    "Free online Word to PDF converter. Convert DOC and DOCX files to PDF while preserving formatting",
  url: "https://freefox.com/word-to-pdf",
  applicationCategory: "UtilitiesApplication",
  operatingSystem: "Any",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
  },
  featureList: [
    "Convert DOC to PDF",
    "Convert DOCX to PDF",
    "Preserve formatting",
    "No file size limits",
  ],
};

export default function WordToPDFPage() {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [showConfetti, setShowConfetti] = useState(false);
  const [affiliateLinks, setAffiliateLinks] = useState<AffiliateLink[]>([]);
  const [showSuccessState, setShowSuccessState] = useState(false);

  // Set page metadata on client side
  useEffect(() => {
    document.title =
      "Free Word to PDF Converter Online - Convert DOC to PDF | FreeFox";

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        "Convert Word documents to PDF online for free. Support for DOC and DOCX files. Preserve formatting and layout. No registration required."
      );
    }

    // Update canonical URL
    let canonical = document.querySelector('link[rel="canonical"]');
    if (!canonical) {
      canonical = document.createElement("link");
      canonical.setAttribute("rel", "canonical");
      document.head.appendChild(canonical);
    }
    canonical.setAttribute("href", "https://freefox.com/word-to-pdf");

    const fetchAffiliateLinks = async () => {
      try {
        // Fetch the JSON file from the public directory
        const response = await fetch("/data/word-to-pdf-affiliate.json");
        const data = await response.json();
        // Set the appropriate data into state
        setAffiliateLinks(data);
      } catch (error) {
        console.error("Failed to fetch affiliate links:", error);
      }
    };

    fetchAffiliateLinks();
  }, []);

  const handleFilesSelected = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleStartOver = () => {
    setFiles([]);
    setShowSuccessState(false);
    setShowConfetti(false);
    setIsProcessing(false);
    setProgress(0);
  };

  const handleConvert = async () => {
    if (files.length === 0) {
      toast.error("Please select a Word document to convert");
      return;
    }

    setIsProcessing(true);
    setProgress(10);

    try {
      const formData = new FormData();
      formData.append("file", files[0]);

      const file = files[0];
      const chunkSize = 1024 * 1024;

      if (file.size > chunkSize) {
        const chunks = Math.ceil(file.size / chunkSize);
        for (let j = 0; j < chunks; j++) {
          // Simulate chunk upload progress
          await new Promise((resolve) => setTimeout(resolve, 50));
          const fileProgress = (j + 1) / chunks;
          const overallProgress = 10 + 70 * fileProgress;
          setProgress(Math.min(80, Math.round(overallProgress)));
        }
      }

      setProgress(80);

      const response = await apiPost("word-to-pdf", formData);

      setProgress(90);

      const fileName = files[0].name.replace(/\.(docx|doc)$/i, ".pdf");

      await downloadFromResponse(response, fileName);

      setProgress(100);
      setShowConfetti(true);
      setShowSuccessState(true);
    } catch (error) {
      console.error("Error converting Word document to PDF:", error);
      toast.error("Failed to convert Word document to PDF. Please try again.");
    } finally {
      setTimeout(() => {
        setIsProcessing(false);
        setProgress(0);
      }, 500);
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { type: "spring", stiffness: 300, damping: 24 },
    },
  };

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <MainLayout>
        <SuccessConfetti
          show={showConfetti}
          duration={3000}
          onComplete={() => setShowConfetti(false)}
        />

        <motion.div
          className="max-w-3xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div className="mb-8 space-y-4" variants={itemVariants}>
            <div className="space-y-2">
              <h1 className="text-4xl font-bold tracking-tight">
                Free Word to PDF Converter Online
              </h1>
              <p className="text-xl text-muted-foreground">
                Convert DOC and DOCX files to PDF while preserving formatting.
                No registration required.
              </p>
            </div>
            <div className="flex flex-wrap gap-2 text-sm text-muted-foreground">
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ DOC & DOCX Support
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Preserve Formatting
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ No File Size Limit
              </span>
              <span className="bg-primary/10 px-2 py-1 rounded">
                ✓ Instant Download
              </span>
            </div>
          </motion.div>

          <motion.div className="space-y-6" variants={itemVariants}>
            <EnhancedFileUpload
              acceptedFileTypes={{
                "application/msword": [".doc"],
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
                  [".docx"],
              }}
              maxFiles={1}
              onFilesSelected={handleFilesSelected}
              isProcessing={isProcessing}
              processingProgress={progress}
              showSuccessState={showSuccessState}
              successMessage="Word document converted successfully and downloaded!"
              successButtonText="Convert Another Word Document"
              onStartOver={handleStartOver}
            />

            {files.length > 0 && !showSuccessState && (
              <div className="flex justify-end">
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    onClick={handleConvert}
                    disabled={isProcessing}
                    className="px-8"
                  >
                    {isProcessing ? "Converting..." : "Convert to PDF"}
                  </Button>
                </motion.div>
              </div>
            )}
          </motion.div>

          {/* Affiliate Section */}
          <motion.div className="mt-6" variants={itemVariants}>
            <AffiliateSection
              title="🔥 Professional Word to PDF Conversion"
              subtitle="Discover top tools for converting Word documents to PDF"
              links={affiliateLinks}
            />
          </motion.div>

          <motion.div
            className="mt-6 bg-muted/50 p-6 rounded-lg"
            variants={itemVariants}
          >
            <h2 className="text-xl font-semibold mb-4">
              About Word to PDF Conversion
            </h2>
            <div className="space-y-4 text-sm text-muted-foreground">
              <p>
                Our Word to PDF converter transforms Microsoft Word documents
                (.doc, .docx) into PDF files while preserving the original
                formatting, fonts, and layout. This ensures your document looks
                the same on any device or platform.
              </p>
              <p>
                PDF files are ideal for sharing documents because they maintain
                their appearance regardless of the software or operating system
                used to view them. They also cannot be easily edited, making
                them perfect for distributing final versions of documents.
              </p>
              <p>
                <strong>Note:</strong> Your files are processed securely on our
                servers and are not stored permanently. They are automatically
                deleted after processing.
              </p>
            </div>
          </motion.div>

          {/* FAQ Section */}
          <motion.div variants={itemVariants}>
            <SEOFaq
              title="Word to PDF Conversion FAQ"
              faqs={wordToPdfFAQs}
              toolName="Word to PDF Converter"
            />
          </motion.div>
        </motion.div>
      </MainLayout>
    </>
  );
}
