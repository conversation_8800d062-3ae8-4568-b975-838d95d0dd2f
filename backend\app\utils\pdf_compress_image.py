"""
Utility function for compressing pdf images.
"""
import io

from app.utils.error_logger import Operation, log_error, log_warning
from PIL import Image


def _compress_page_images(page, quality=30, dpi=72):
    """
    Aggressively compress images within a PDF page for maximum size reduction:
    1. Extracting images
    2. Significant downsampling to target DPI
    3. Heavy JPEG compression with low quality
    4. Skip small images that won't benefit from compression
    5. Apply image processing optimizations

    Returns the modified page object
    """
    try:
        # A single 'page' object is passed, so we get images directly from it.
        images = page.get_images(full=True)
        for img_index, img in enumerate(images):
            xref = img[0]
            try:
                base_image = page.parent.extract_image(xref)
            except Exception as e:
                # This can happen for inline images
                log_warning(f"Could not extract image {xref}: {str(e)}", Operation.FAILURE)
                continue

            if not base_image or "image" not in base_image:
                continue

            image_bytes = base_image["image"]
            img_pil = Image.open(io.BytesIO(image_bytes)).convert("RGB")

            # Resize image to target DPI
            img_pil = img_pil.resize(
                [int(dpi * size / 72) for size in img_pil.size],
                Image.LANCZOS
            )

            # Recompress and replace
            img_buffer = io.BytesIO()
            img_pil.save(img_buffer, format="JPEG", quality=quality, optimize=True)
            page.parent.replace_image(xref, stream=img_buffer.getvalue())

    except ImportError as e:
        # If PIL is not available, return original page
        log_warning(f"Required library not available for image compression: {str(e)}", Operation.FILE_NOT_FOUND)
    except Exception as e:
        log_error(f"An error occurred during image compression: {str(e)}", exc=e, operation=Operation.IMAGE_COMPRESS)

    return page