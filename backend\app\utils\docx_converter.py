"""
Utility functions for converting Word documents to PDF.
"""
import os
import platform

from app.utils.error_logger import Operation, log_error, log_info


def convert_with_docx2pdf(input_path, output_path):
    """
    Convert a Word document to PDF using docx2pdf.
    This requires Microsoft Word to be installed on Windows.
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        from docx2pdf import convert
        log_info(f"Converting {input_path} to {output_path} using docx2pdf")
        convert(input_path, output_path)
        return os.path.exists(output_path)
    except Exception as e:
        log_error(f"docx2pdf conversion failed: {str(e)}", exc=e, operation=Operation.FAILURE)
        return False

def convert_with_python_docx(input_path, output_path):
    """
    Convert a Word document to PDF using python-docx and reportlab.
    This is a fallback method that extracts text only (no formatting or images).
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    try:
        import docx
        from reportlab.lib.pagesizes import letter
        from reportlab.lib.styles import ParagraphStyle, getSampleStyleSheet
        from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer
        
        log_info(f"Converting {input_path} to {output_path} using python-docx and reportlab")
        
        # Read the Word document
        doc = docx.Document(input_path)
        
        # Create a PDF
        pdf = SimpleDocTemplate(
            output_path,
            pagesize=letter,
            title=f"Converted from {os.path.basename(input_path)}"
        )
        
        # Set up styles
        styles = getSampleStyleSheet()
        styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=styles['Title'],
            fontSize=24,
            spaceAfter=24
        ))
        
        # Extract text and create PDF content
        content = []
        content.append(Paragraph(f"Converted from: {os.path.basename(input_path)}", styles['CustomTitle']))
        content.append(Spacer(1, 12))
        
        # Add paragraphs from the Word document
        for para in doc.paragraphs:
            if para.text:
                content.append(Paragraph(para.text, styles['Normal']))
                content.append(Spacer(1, 6))
        
        # Build the PDF
        pdf.build(content)
        
        return os.path.exists(output_path)
    except Exception as e:
        log_error(f"python-docx conversion failed: {str(e)}", exc=e, operation=Operation.FAILURE)
        return False

def convert_docx_to_pdf(input_path, output_path):
    """
    Convert a Word document to PDF using the best available method.
    
    Args:
        input_path (str): Path to the input Word document
        output_path (str): Path to save the output PDF
        
    Returns:
        bool: True if conversion was successful, False otherwise
    """
    # Try docx2pdf first (best results but requires MS Word on Windows)
    if platform.system() == 'Windows':
        if convert_with_docx2pdf(input_path, output_path):
            return True
    
    # Try python-docx fallback (text only)
    if convert_with_python_docx(input_path, output_path):
        return True
    
    return False
